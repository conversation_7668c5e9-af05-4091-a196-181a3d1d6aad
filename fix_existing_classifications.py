#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix existing classifications in the CSV file.
Reclassifies financial messages without amounts as non-financial.
"""

import csv
import asyncio
from classifiers import SMSClassifier

async def fix_classifications():
    """Fix existing classifications in the CSV file."""

    print("Loading existing CSV file...")

    # Read the CSV file
    rows = []
    with open('sms_processed_results.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        for row in reader:
            rows.append(row)

    print(f"Total records: {len(rows)}")

    # Find financial messages with no amount
    financial_no_amount = []
    for i, row in enumerate(rows):
        if (row['classification'] == 'financial' and
            (not row['amount'] or row['amount'].strip() == '' or row['amount'] == '0')):
            financial_no_amount.append((i, row))

    print(f"Financial messages with no amount: {len(financial_no_amount)}")

    if len(financial_no_amount) == 0:
        print("No messages to fix!")
        return

    classifier = SMSClassifier()
    fixed_count = 0
    fixed_examples = []

    print("Processing messages...")

    for idx, (row_idx, row) in enumerate(financial_no_amount):
        original_text = row['original_text']

        # Re-classify the message
        initial_classification = classifier.classify_sms(original_text)

        # Validate with empty amount
        validated_classification = classifier.validate_financial_classification(initial_classification, None)

        # If reclassified as non-financial, update the row
        if validated_classification['sms_type'] == 'Other':
            rows[row_idx]['classification'] = 'non-financial'
            rows[row_idx]['sms_type'] = 'Other'
            rows[row_idx]['sms_event_subtype'] = 'Non-Financial'
            rows[row_idx]['sms_info_type'] = 'Other'
            rows[row_idx]['extracted_data_json'] = '{}'
            rows[row_idx]['amount'] = ''
            rows[row_idx]['date'] = ''
            rows[row_idx]['account_number'] = ''
            rows[row_idx]['bank_name'] = ''
            rows[row_idx]['txn_ref'] = ''
            rows[row_idx]['currency'] = ''
            fixed_count += 1

            if len(fixed_examples) < 5:
                fixed_examples.append(original_text)

            if fixed_count % 100 == 0:
                print(f"Fixed {fixed_count} messages...")

    print(f"Fixed {fixed_count} messages total")

    # Save the corrected CSV
    output_file = 'sms_processed_results_fixed.csv'
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)

    print(f"Saved corrected data to {output_file}")

    # Print summary statistics
    financial_count = sum(1 for row in rows if row['classification'] == 'financial')
    non_financial_count = sum(1 for row in rows if row['classification'] == 'non-financial')

    print("\nSummary after fixes:")
    print(f"Total records: {len(rows)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")

    # Show some examples of fixed messages
    print("\nExamples of fixed messages:")
    for text in fixed_examples:
        print(f"- {text[:80]}{'...' if len(text) > 80 else ''}")

if __name__ == "__main__":
    asyncio.run(fix_classifications())
