# SMS Backup Processing System

This system processes SMS backup CSV files and generates comprehensive classification and data extraction results.

## Features

- **Financial vs Non-Financial Classification**: Automatically classifies SMS messages as financial or non-financial
- **Detailed Type Classification**: Categorizes financial SMS into types (Purchase, Payment, Deposit & Withdrawal, Accounts, Investment)
- **Subtype Classification**: Further classifies into subtypes (UPI, Debit Card, EMI Payment, etc.)
- **Info Type Classification**: Determines information type (Outflow, Inflow, Account Status, etc.)
- **Data Extraction**: Extracts structured data like amounts, dates, account numbers, transaction references
- **Batch Processing**: Efficiently processes large CSV files with progress tracking
- **Comprehensive Reporting**: Generates detailed summary reports and statistics

## Quick Start

### 1. Basic Usage (Auto-detect input file)
```bash
python run_sms_processing.py
```
This will automatically use `sms_backup.csv` if available, otherwise `SMS-Data.csv`.

### 2. Specify Input File
```bash
python run_sms_processing.py your_sms_backup.csv
```

### 3. Specify Both Input and Output Files
```bash
python run_sms_processing.py input.csv output_results.csv
```

### 4. Test with Sample Data
```bash
python test_sms_processing.py
```
This creates sample SMS data and demonstrates the processing.

## Input CSV Format

The input CSV file should have the following columns:
- `phoneNumber`: Phone number (can be masked)
- `id`: Unique message ID
- `updateAt`: Timestamp
- `senderAddress`: SMS sender
- `text`: SMS message content

Example:
```csv
phoneNumber,id,updateAt,senderAddress,text
xx39973810,msg-001,"Mon, 1 Jan 2024 10:00:00 UTC",HDFCBK,"Rs.500.00 debited from A/c **1234..."
```

## Output CSV Structure

The output CSV contains the following columns:

### Basic Information
- `original_id`: Original message ID
- `phone_number`: Phone number
- `sender_address`: SMS sender
- `timestamp`: Message timestamp
- `original_text`: Original SMS text

### Classification Results
- `classification`: **financial** or **non-financial**
- `sms_type`: Main category (Purchase, Payment, Deposit & Withdrawal, Accounts, Investment, Other)
- `sms_event_subtype`: Subcategory (UPI, Debit Card, EMI Payment, Monthly Salary Credit, etc.)
- `sms_info_type`: Information type (Outflow, Inflow, Account Status, etc.)

### Extracted Data
- `extracted_data_json`: All extracted fields as JSON
- `amount`: Transaction amount
- `date`: Transaction date
- `account_number`: Account number
- `bank_name`: Bank name
- `txn_ref`: Transaction reference
- `currency`: Currency (usually INR)

### Processing Status
- `processing_status`: success/error
- `error_message`: Error details if any

## Classification Categories

### SMS Types (Main Categories)
- **Purchase**: UPI payments, card transactions
- **Payment**: EMI payments, recharges
- **Deposit & Withdrawal**: Salary credits, loan disbursals, withdrawals
- **Accounts**: Account balance, status updates
- **Investment**: Trading, mutual funds, SIP
- **Other**: Non-financial or unrecognized messages

### Event Subtypes
- **Purchase**: UPI, Debit Card, Credit Card
- **Payment**: EMI Payment, Recharge
- **Deposit & Withdrawal**: Monthly Salary Credit, Loan Disbursal, ATM Withdrawal
- **Accounts**: Bank Account, Loan
- **Investment**: Investment

### Info Types
- **Outflow**: Money going out (debits, payments, purchases)
- **Inflow**: Money coming in (credits, salary, refunds)
- **Account Status**: Balance updates, account information
- **Application**: Loan applications, requests
- **Other**: Miscellaneous

## Extracted Data Fields

Depending on the SMS type, the system extracts various fields:

### Common Fields
- Amount, Date, Account Number, Bank Name, Transaction Reference, Currency

### UPI Specific
- UPI Recipient, UPI Reference

### Card Specific
- Merchant Name, Card Number (masked)

### EMI Specific
- Loan ID, EMI Amount, Outstanding Amount

### Salary Specific
- Employer Name, Salary Flag

### Investment Specific
- Trading Account, Traded Value, Trading Date

## Performance

- Processes SMS messages in configurable batches (default: 100)
- Concurrent processing for better performance
- Progress tracking with percentage completion
- Memory efficient for large datasets

## Output Files

1. **Main Results CSV**: `sms_processed_results.csv` (or specified output file)
2. **Summary JSON**: `sms_processed_results_summary.json` with statistics
3. **Console Report**: Detailed summary printed to console

## Example Usage in Code

```python
from sms_backup_processor import SMSBackupProcessor
import asyncio

# Async usage
async def process_sms():
    processor = SMSBackupProcessor('my_sms.csv', 'results.csv')
    await processor.run_full_processing()

# Sync usage
from sms_backup_processor import process_sms_backup_sync
process_sms_backup_sync('my_sms.csv', 'results.csv')
```

## Requirements

- Python 3.7+
- All dependencies from `requirements.txt`
- Existing SMS parser modules (`sms_parser.py`, `classifiers.py`, `field_extractors.py`, `utils.py`)

## Error Handling

- Graceful handling of malformed SMS messages
- Error logging with specific error messages
- Partial processing continues even if some messages fail
- Detailed error reporting in output CSV

## Tips for Best Results

1. Ensure your CSV file has the correct column names
2. For large files, processing may take several minutes
3. Check the summary report for processing statistics
4. Review error messages for any failed records
5. The system works best with Indian banking SMS formats
