#!/usr/bin/env python3
"""
<PERSON>ript to fix financial OTP and e-mandate messages that were incorrectly classified as non-financial.
"""

import csv
import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def fix_financial_otp_emandates():
    """Fix financial OTP and e-mandate messages in the CSV file."""
    
    print("Loading existing CSV file...")
    
    # Read the CSV file
    rows = []
    with open('sms_processed_results_final.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        for row in reader:
            rows.append(row)
    
    print(f"Total records: {len(rows)}")
    
    # Find non-financial messages that might be financial OTPs or e-mandates
    potential_financial = []
    for i, row in enumerate(rows):
        if row['classification'] == 'non-financial':
            text = row['original_text'].lower()
            # Look for potential financial OTP or e-mandate messages
            if (('otp' in text and ('trxn' in text or 'transaction' in text or 'purchase' in text)) or
                ('e-mandate' in text) or
                ('payment e-mandate' in text) or
                ('authorised debit' in text)):
                potential_financial.append((i, row))
    
    print(f"Potential financial messages found in non-financial: {len(potential_financial)}")
    
    if len(potential_financial) == 0:
        print("No messages to fix!")
        return
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    fixed_count = 0
    fixed_examples = []
    
    print("Processing potential financial messages...")
    
    for idx, (row_idx, row) in enumerate(potential_financial):
        original_text = row['original_text']
        
        # Re-classify the message
        initial_classification = classifier.classify_sms(original_text)
        
        # Extract amount
        amount = await extractor.extract_amount(original_text)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        # If reclassified as financial, update the row
        if validated_classification['sms_type'] != 'Other':
            # Extract all financial fields
            date = await extractor.extract_date(original_text)
            account_number = await extractor.extract_account_number(original_text)
            bank_name = await extractor.extract_bank_name(original_text)
            txn_ref = await extractor.extract_transaction_ref(original_text)
            currency = await extractor.extract_currency(original_text)
            
            # Update the row
            rows[row_idx]['classification'] = 'financial'
            rows[row_idx]['sms_type'] = validated_classification['sms_type']
            rows[row_idx]['sms_event_subtype'] = validated_classification['sms_event_subtype']
            rows[row_idx]['sms_info_type'] = validated_classification['sms_info_type']
            rows[row_idx]['amount'] = amount or ''
            rows[row_idx]['date'] = date or ''
            rows[row_idx]['account_number'] = account_number or ''
            rows[row_idx]['bank_name'] = bank_name or ''
            rows[row_idx]['txn_ref'] = txn_ref or ''
            rows[row_idx]['currency'] = currency or 'INR'
            
            # Create extracted data JSON
            extracted_data = {
                'sms_type': validated_classification['sms_type'],
                'sms_event_subtype': validated_classification['sms_event_subtype'],
                'sms_info_type': validated_classification['sms_info_type'],
                'amount': amount or '',
                'date': date or '',
                'account_number': account_number or '',
                'bank_name': bank_name or '',
                'txn_ref': txn_ref or '',
                'currency': currency or 'INR'
            }
            
            import json
            rows[row_idx]['extracted_data_json'] = json.dumps(extracted_data, ensure_ascii=False)
            
            fixed_count += 1
            
            if len(fixed_examples) < 5:
                fixed_examples.append(original_text)
            
            print(f"Fixed: {original_text[:80]}{'...' if len(original_text) > 80 else ''}")
            print(f"  Amount: {amount}, Classification: {validated_classification}")
    
    print(f"Fixed {fixed_count} messages total")
    
    # Save the corrected CSV
    output_file = 'sms_processed_results_corrected.csv'
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"Saved corrected data to {output_file}")
    
    # Print summary statistics
    financial_count = sum(1 for row in rows if row['classification'] == 'financial')
    non_financial_count = sum(1 for row in rows if row['classification'] == 'non-financial')
    
    print("\nSummary after fixes:")
    print(f"Total records: {len(rows)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    
    # Show some examples of fixed messages
    print("\nExamples of fixed messages:")
    for text in fixed_examples:
        print(f"- {text[:80]}{'...' if len(text) > 80 else ''}")
    
    if fixed_count > 0:
        print(f"\n✅ SUCCESS: {fixed_count} financial OTP/e-mandate messages have been correctly reclassified!")
    else:
        print("\n✅ No messages needed fixing - all classifications are correct!")

if __name__ == "__main__":
    asyncio.run(fix_financial_otp_emandates())
