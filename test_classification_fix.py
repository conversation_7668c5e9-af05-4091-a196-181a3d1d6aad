#!/usr/bin/env python3
"""
Test script to verify the classification fix for messages without amounts.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def test_classification_fix():
    """Test the classification fix for problematic messages."""
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    # Test messages that were incorrectly classified as financial
    test_messages = [
        "DO NOT SHARE: Your Google Pay OTP is 904958. Message ID: f6f01vO3FNp",
        "SBIUPI R8ibGLnAIOCkH5F3TOh2ai%2FMgk1CzypJeVUHtkLHjSsf8RiDpv8Upwuw0NgiaKPv!99998f39bf2c7de609a84c5020caa85f05b4b7d82413e655b373d905f1493805",
        "HDFCUPI 1MEEgA0jaQzrVDM9ELtEcztxficPxr07vSVy9RIV7g3BGXIeJg3VcE0Yas63ksWd",
        "JP7 WTxGO0*4c3dhlQvDrQ2*6U*E7153*lH0E1p*8**ho*A*",
        "JP7 7$8&N2!!%*!5%N#p!&W&!3!%v%&IQ8%***43Q*$%",
        "I'll call you back.",
        "211620 is your Zomato verification code. Enjoy :-) AlOe4TVDpqm -ZOMATO",
        "Spotify code: 905036. Valid for 5 minutes",
        "Hello! Please tap this link to finish joining our group \"Bowling\" on Splitwise:",
        "CREDUPI ppA9bayG0ZZX9zQaLy11*cO4GdxIiviu--t&0",
        
        # Test a valid financial message for comparison
        "Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno 448771760816. If not u? call 1800111109. -SBI"
    ]
    
    print("Testing classification fix for messages without amounts...")
    print("=" * 80)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\nTest {i}: {message[:60]}{'...' if len(message) > 60 else ''}")
        print("-" * 80)
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        print(f"Initial classification: {initial_classification}")
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        print(f"Extracted amount: '{amount}'")
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        print(f"Validated classification: {validated_classification}")
        
        # Check if classification changed
        if initial_classification != validated_classification:
            print("✅ FIXED: Classification changed from financial to non-financial")
        elif validated_classification['sms_type'] == 'Other':
            print("✅ CORRECT: Already classified as non-financial")
        else:
            print("✅ VALID: Financial message with amount extracted")

if __name__ == "__main__":
    asyncio.run(test_classification_fix())
