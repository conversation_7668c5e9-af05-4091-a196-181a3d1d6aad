#!/usr/bin/env python3
"""
Complete SMS Processing Workflow Demonstration

This script demonstrates the complete workflow of processing SMS backup CSV files
and generating classification and extraction results.
"""

import os
import csv
import json
from sms_backup_processor import process_sms_backup_sync


def create_comprehensive_demo_data():
    """
    Create a comprehensive demo SMS backup CSV with various message types.
    """
    demo_sms_data = [
        # Financial SMS - Purchase/UPI
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-001',
            'updateAt': 'Mon, 1 Jan 2024 10:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Rs.1,500.00 debited from A/c **1234 on 01-01-24 via UPI-PAYTM to MERCHANT@paytm. Avl Bal Rs.25,000.00. UPI Ref ************.'
        },
        # Financial SMS - Purchase/Card
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-002',
            'updateAt': 'Mon, 1 Jan 2024 11:00:00 UTC',
            'senderAddress': 'SBICARD',
            'text': 'Rs.2,500.00 spent on AMAZON INDIA using SBI Card **5678 on 01-01-24 at 11:00. Avl limit Rs.47,500.00'
        },
        # Financial SMS - Payment/EMI
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-003',
            'updateAt': 'Mon, 1 Jan 2024 12:00:00 UTC',
            'senderAddress': 'ICICIBANK',
            'text': 'Your EMI of Rs.8,500.00 for Personal Loan A/c ********* has been debited from A/c **4567 on 01-01-24. Outstanding: Rs.85,000.00'
        },
        # Financial SMS - Deposit/Salary
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-004',
            'updateAt': 'Mon, 1 Jan 2024 13:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Salary of Rs.95,000.00 credited to A/c **1234 on 01-01-24 from TECH SOLUTIONS PVT LTD. Avl Bal Rs.1,20,000.00'
        },
        # Financial SMS - Payment/Recharge
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-005',
            'updateAt': 'Mon, 1 Jan 2024 14:00:00 UTC',
            'senderAddress': 'JIOMNY',
            'text': 'Rs.399.00 recharge successful for Jio number **********. Plan: 2GB/day for 84 days. Validity: 84 days. Thanks for choosing Jio!'
        },
        # Financial SMS - Investment
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-006',
            'updateAt': 'Mon, 1 Jan 2024 15:00:00 UTC',
            'senderAddress': 'NSEIND',
            'text': 'Dear CLIENT123, Traded value Rs.25,000.00 on 01-01-24. Buy: RELIANCE 10 shares @ Rs.2500. Check your registered email for contract note.'
        },
        # Financial SMS - Accounts/Balance
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-007',
            'updateAt': 'Mon, 1 Jan 2024 16:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Your A/c **1234 balance is Rs.1,20,000.00 as on 01-01-24 16:00. Available balance Rs.1,15,000.00. For mini statement, SMS BAL to 5676712.'
        },
        # Financial SMS - Deposit/Loan Disbursal
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-008',
            'updateAt': 'Mon, 1 Jan 2024 17:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Personal Loan of Rs.5,00,000.00 disbursed to A/c **1234 on 01-01-24. Loan A/c: PL*********. EMI: Rs.12,500.00 starts from 01-02-24.'
        },
        # Non-Financial SMS - OTP
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-009',
            'updateAt': 'Mon, 1 Jan 2024 18:00:00 UTC',
            'senderAddress': 'OTPVERIFY',
            'text': 'Your OTP for transaction verification is 456789. Do not share this OTP with anyone. Valid for 10 minutes only.'
        },
        # Non-Financial SMS - Promotional
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-010',
            'updateAt': 'Mon, 1 Jan 2024 19:00:00 UTC',
            'senderAddress': 'OFFERS',
            'text': 'MEGA SALE! Get up to 70% off on electronics, fashion & more. Shop now at www.example.com. Use code SAVE70. Limited time offer!'
        },
        # Non-Financial SMS - Service Update
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-011',
            'updateAt': 'Mon, 1 Jan 2024 20:00:00 UTC',
            'senderAddress': 'JIO',
            'text': 'Dear customer, you have consumed 80% of your daily data quota as on 01-01-24 20:00. To continue enjoying high-speed data, recharge now.'
        },
        # Financial SMS - Complex UPI with multiple details
        {
            'phoneNumber': 'xx39973810',
            'id': 'demo-012',
            'updateAt': 'Mon, 1 Jan 2024 21:00:00 UTC',
            'senderAddress': 'SBIUPI',
            'text': 'UPI transaction of Rs.750.00 to SWIGGY@paytm successful on 01-01-24. From A/c **9876. UPI Ref: *********012. Balance: Rs.24,250.00'
        }
    ]
    
    # Write demo data to CSV
    with open('demo_sms_backup.csv', 'w', newline='', encoding='utf-8') as file:
        fieldnames = ['phoneNumber', 'id', 'updateAt', 'senderAddress', 'text']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(demo_sms_data)
    
    print(f"✅ Created demo_sms_backup.csv with {len(demo_sms_data)} diverse SMS messages")
    return len(demo_sms_data)


def analyze_results(results_file: str, summary_file: str):
    """
    Analyze and display the processing results.
    """
    print("\n" + "="*60)
    print("📊 DETAILED RESULTS ANALYSIS")
    print("="*60)
    
    # Load and display summary
    if os.path.exists(summary_file):
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        print(f"📈 Processing Statistics:")
        print(f"  • Total Messages: {summary['total_records']:,}")
        print(f"  • Financial Messages: {summary['financial_sms']:,} ({summary['financial_percentage']:.1f}%)")
        print(f"  • Non-Financial Messages: {summary['non_financial_sms']:,}")
        print(f"  • Successful Extractions: {summary['successful_extractions']:,}")
        print(f"  • Extraction Success Rate: {summary['extraction_success_rate']:.1f}%")
    
    # Show sample results
    if os.path.exists(results_file):
        print(f"\n📋 Sample Results from {results_file}:")
        with open(results_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            financial_count = 0
            non_financial_count = 0
            
            for row in reader:
                if row['classification'] == 'financial' and financial_count < 3:
                    print(f"\n  💰 Financial SMS #{financial_count + 1}:")
                    print(f"     Sender: {row['sender_address']}")
                    print(f"     Type: {row['sms_type']} → {row['sms_event_subtype']} → {row['sms_info_type']}")
                    if row['amount']:
                        print(f"     Amount: ₹{row['amount']} | Date: {row['date']}")
                    if row['account_number']:
                        print(f"     Account: {row['account_number']}")
                    print(f"     Text: {row['original_text'][:80]}...")
                    financial_count += 1
                
                elif row['classification'] == 'non-financial' and non_financial_count < 2:
                    print(f"\n  📝 Non-Financial SMS #{non_financial_count + 1}:")
                    print(f"     Sender: {row['sender_address']}")
                    print(f"     Type: {row['sms_type']} → {row['sms_event_subtype']}")
                    print(f"     Text: {row['original_text'][:80]}...")
                    non_financial_count += 1
                
                if financial_count >= 3 and non_financial_count >= 2:
                    break


def main():
    """
    Main function to demonstrate the complete SMS processing workflow.
    """
    print("🚀 SMS Processing Complete Workflow Demo")
    print("=" * 50)
    
    # Step 1: Create comprehensive demo data
    print("\n📝 Step 1: Creating comprehensive demo SMS data...")
    create_comprehensive_demo_data()
    
    # Step 2: Process the SMS backup
    print("\n🔄 Step 2: Processing SMS backup with classification and extraction...")
    process_sms_backup_sync('demo_sms_backup.csv', 'demo_results.csv')
    
    # Step 3: Analyze results
    print("\n📊 Step 3: Analyzing results...")
    analyze_results('demo_results.csv', 'demo_results_summary.json')
    
    # Step 4: Show file outputs
    print(f"\n📁 Generated Files:")
    files_info = [
        ('demo_sms_backup.csv', 'Input SMS backup file'),
        ('demo_results.csv', 'Main results with classification and extracted data'),
        ('demo_results_summary.json', 'Summary statistics and analysis')
    ]
    
    for filename, description in files_info:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  • {filename} ({size:,} bytes) - {description}")
    
    print(f"\n✅ Demo completed successfully!")
    print(f"💡 You can now:")
    print(f"   • Open demo_results.csv to see detailed classification and extraction results")
    print(f"   • Review demo_results_summary.json for processing statistics")
    print(f"   • Use run_sms_processing.py with your own sms_backup.csv file")


if __name__ == "__main__":
    main()
