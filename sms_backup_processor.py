import csv
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import os

from sms_parser import <PERSON><PERSON><PERSON><PERSON>
from classifiers import SMSClassifier
from utils import clean_text


class SMSBackupProcessor:
    """
    Process SMS backup CSV file and generate classification and extraction results.
    """
    
    def __init__(self, input_csv_path: str = 'sms_backup.csv', output_csv_path: str = 'sms_processed_results.csv'):
        self.input_csv_path = input_csv_path
        self.output_csv_path = output_csv_path
        self.parser = SMSParser()
        self.classifier = SMSClassifier()
        
        # Define the output CSV columns
        self.output_columns = [
            'original_id',
            'phone_number', 
            'sender_address',
            'timestamp',
            'original_text',
            'classification',  # financial/non-financial
            'sms_type',       # types
            'sms_event_subtype',  # sub-types
            'sms_info_type',  # info types
            'extracted_data_json',  # All extracted data as JSON
            # Common extracted fields as separate columns
            'amount',
            'date',
            'account_number',
            'bank_name',
            'txn_ref',
            'currency',
            'processing_status',
            'error_message'
        ]
    
    def read_sms_backup_csv(self) -> List[Dict[str, Any]]:
        """
        Read SMS backup CSV file and return list of SMS records.
        """
        sms_records = []
        
        if not os.path.exists(self.input_csv_path):
            print(f"❌ Input file {self.input_csv_path} not found!")
            return []
        
        try:
            # Read file and clean NUL characters
            with open(self.input_csv_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read().replace('\x00', '')  # Remove NUL characters

            # Parse cleaned content
            from io import StringIO
            cleaned_file = StringIO(content)
            reader = csv.DictReader(cleaned_file)

            for row_num, row in enumerate(reader, 1):
                try:
                    # Handle different possible column names
                    sms_text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    timestamp = row.get('updateAt', '').strip()
                    phone = row.get('phoneNumber', '').strip()
                    msg_id = row.get('id', str(row_num))

                    if sms_text:  # Only process non-empty SMS
                        sms_records.append({
                            'id': msg_id,
                            'phone_number': phone,
                            'sender_address': sender,
                            'timestamp': timestamp,
                            'text': sms_text,
                            'row_number': row_num
                        })
                except Exception as row_error:
                    print(f"⚠️ Skipping row {row_num} due to error: {row_error}")
                    continue

        except Exception as e:
            print(f"❌ Error reading CSV file: {e}")
            return []
        
        print(f"📱 Successfully read {len(sms_records)} SMS records from {self.input_csv_path}")
        return sms_records
    
    async def process_single_sms(self, sms_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single SMS record and return classification and extraction results.
        """
        sms_text = sms_record['text']
        result = {
            'original_id': sms_record['id'],
            'phone_number': sms_record['phone_number'],
            'sender_address': sms_record['sender_address'],
            'timestamp': sms_record['timestamp'],
            'original_text': sms_text,
            'processing_status': 'success',
            'error_message': ''
        }
        
        try:
            # Clean the SMS text
            cleaned_text = clean_text(sms_text)
            
            # First classify to determine if financial or non-financial
            classification = self.classifier.classify_sms(cleaned_text)
            
            # Determine if financial or non-financial
            if classification['sms_type'] == 'Other' and classification['sms_event_subtype'] == 'Non-Financial':
                result.update({
                    'classification': 'non-financial',
                    'sms_type': 'Other',
                    'sms_event_subtype': 'Non-Financial',
                    'sms_info_type': 'Other',
                    'extracted_data_json': '{}',
                    'amount': '',
                    'date': '',
                    'account_number': '',
                    'bank_name': '',
                    'txn_ref': '',
                    'currency': ''
                })
            else:
                # Financial SMS - use full parser
                parsed_results = await self.parser.parse_sms(sms_text)
                
                if parsed_results:
                    # Take the first result if multiple segments found
                    parsed_data = parsed_results[0]
                    
                    # Extract common fields
                    extracted_data = {k: v for k, v in parsed_data.items() 
                                    if k not in ['sms_type', 'sms_event_subtype', 'sms_info_type']}
                    
                    result.update({
                        'classification': 'financial',
                        'sms_type': parsed_data.get('sms_type', ''),
                        'sms_event_subtype': parsed_data.get('sms_event_subtype', ''),
                        'sms_info_type': parsed_data.get('sms_info_type', ''),
                        'extracted_data_json': json.dumps(extracted_data, ensure_ascii=False),
                        'amount': parsed_data.get('amount', ''),
                        'date': parsed_data.get('date', ''),
                        'account_number': parsed_data.get('account_number', ''),
                        'bank_name': parsed_data.get('bank_name', ''),
                        'txn_ref': parsed_data.get('txn_ref', ''),
                        'currency': parsed_data.get('currency', '')
                    })
                else:
                    # Classified as financial but no data extracted
                    # Validate classification - if no amount, reclassify as non-financial
                    validated_classification = self.classifier.validate_financial_classification(classification, None)

                    if validated_classification['sms_type'] == 'Other':
                        # Reclassified as non-financial
                        result.update({
                            'classification': 'non-financial',
                            'sms_type': 'Other',
                            'sms_event_subtype': 'Non-Financial',
                            'sms_info_type': 'Other',
                            'extracted_data_json': '{}',
                            'amount': '',
                            'date': '',
                            'account_number': '',
                            'bank_name': '',
                            'txn_ref': '',
                            'currency': ''
                        })
                    else:
                        # Keep as financial with empty fields
                        result.update({
                            'classification': 'financial',
                            'sms_type': classification['sms_type'],
                            'sms_event_subtype': classification['sms_event_subtype'],
                            'sms_info_type': classification['sms_info_type'],
                            'extracted_data_json': '{}',
                            'amount': '',
                            'date': '',
                            'account_number': '',
                            'bank_name': '',
                            'txn_ref': '',
                            'currency': ''
                        })
                    
        except Exception as e:
            result.update({
                'classification': 'error',
                'sms_type': '',
                'sms_event_subtype': '',
                'sms_info_type': '',
                'extracted_data_json': '{}',
                'amount': '',
                'date': '',
                'account_number': '',
                'bank_name': '',
                'txn_ref': '',
                'currency': '',
                'processing_status': 'error',
                'error_message': str(e)
            })
        
        return result

    async def process_all_sms(self, sms_records: List[Dict[str, Any]], batch_size: int = 100) -> List[Dict[str, Any]]:
        """
        Process all SMS records in batches for better performance.
        """
        results = []
        total_records = len(sms_records)

        print(f"🔄 Processing {total_records} SMS records in batches of {batch_size}...")

        for i in range(0, total_records, batch_size):
            batch = sms_records[i:i + batch_size]
            batch_results = []

            # Process batch concurrently
            tasks = [self.process_single_sms(record) for record in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle any exceptions
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    error_result = {
                        'original_id': batch[j]['id'],
                        'phone_number': batch[j]['phone_number'],
                        'sender_address': batch[j]['sender_address'],
                        'timestamp': batch[j]['timestamp'],
                        'original_text': batch[j]['text'],
                        'classification': 'error',
                        'sms_type': '',
                        'sms_event_subtype': '',
                        'sms_info_type': '',
                        'extracted_data_json': '{}',
                        'amount': '',
                        'date': '',
                        'account_number': '',
                        'bank_name': '',
                        'txn_ref': '',
                        'currency': '',
                        'processing_status': 'error',
                        'error_message': str(result)
                    }
                    results.append(error_result)
                else:
                    results.append(result)

            # Progress update
            processed = min(i + batch_size, total_records)
            print(f"✅ Processed {processed}/{total_records} records ({processed/total_records*100:.1f}%)")

        return results

    def write_results_to_csv(self, results: List[Dict[str, Any]]) -> None:
        """
        Write processing results to output CSV file.
        """
        try:
            with open(self.output_csv_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=self.output_columns)
                writer.writeheader()

                for result in results:
                    # Ensure all columns are present
                    row = {col: result.get(col, '') for col in self.output_columns}
                    writer.writerow(row)

            print(f"💾 Results saved to {self.output_csv_path}")

        except Exception as e:
            print(f"❌ Error writing results to CSV: {e}")

    def generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary report of the processing results.
        """
        total_records = len(results)
        financial_count = sum(1 for r in results if r['classification'] == 'financial')
        non_financial_count = sum(1 for r in results if r['classification'] == 'non-financial')
        error_count = sum(1 for r in results if r['classification'] == 'error')

        # Count by SMS types
        sms_type_counts = {}
        subtype_counts = {}
        info_type_counts = {}

        for result in results:
            if result['classification'] == 'financial':
                sms_type = result['sms_type']
                subtype = result['sms_event_subtype']
                info_type = result['sms_info_type']

                sms_type_counts[sms_type] = sms_type_counts.get(sms_type, 0) + 1
                subtype_counts[subtype] = subtype_counts.get(subtype, 0) + 1
                info_type_counts[info_type] = info_type_counts.get(info_type, 0) + 1

        # Count successful extractions
        successful_extractions = sum(1 for r in results
                                   if r['classification'] == 'financial' and r['amount'])

        summary = {
            'total_records': total_records,
            'financial_sms': financial_count,
            'non_financial_sms': non_financial_count,
            'error_records': error_count,
            'successful_extractions': successful_extractions,
            'financial_percentage': (financial_count / total_records * 100) if total_records > 0 else 0,
            'extraction_success_rate': (successful_extractions / financial_count * 100) if financial_count > 0 else 0,
            'sms_type_distribution': sms_type_counts,
            'subtype_distribution': subtype_counts,
            'info_type_distribution': info_type_counts
        }

        return summary

    def print_summary_report(self, summary: Dict[str, Any]) -> None:
        """
        Print a formatted summary report.
        """
        print("\n" + "="*60)
        print("📊 SMS PROCESSING SUMMARY REPORT")
        print("="*60)

        print(f"📱 Total SMS Records: {summary['total_records']:,}")
        print(f"💰 Financial SMS: {summary['financial_sms']:,} ({summary['financial_percentage']:.1f}%)")
        print(f"📝 Non-Financial SMS: {summary['non_financial_sms']:,}")
        print(f"❌ Error Records: {summary['error_records']:,}")
        print(f"✅ Successful Extractions: {summary['successful_extractions']:,}")
        print(f"📈 Extraction Success Rate: {summary['extraction_success_rate']:.1f}%")

        print(f"\n🏷️ SMS Type Distribution:")
        for sms_type, count in sorted(summary['sms_type_distribution'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {sms_type}: {count:,}")

        print(f"\n🔖 Subtype Distribution (Top 10):")
        sorted_subtypes = sorted(summary['subtype_distribution'].items(), key=lambda x: x[1], reverse=True)[:10]
        for subtype, count in sorted_subtypes:
            print(f"  {subtype}: {count:,}")

        print(f"\n📋 Info Type Distribution:")
        for info_type, count in sorted(summary['info_type_distribution'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {info_type}: {count:,}")

        print("="*60)

    async def run_full_processing(self) -> None:
        """
        Run the complete SMS backup processing pipeline.
        """
        print("🚀 Starting SMS Backup Processing...")
        start_time = datetime.now()

        # Step 1: Read SMS backup CSV
        sms_records = self.read_sms_backup_csv()
        if not sms_records:
            print("❌ No SMS records found. Exiting.")
            return

        # Step 2: Process all SMS records
        results = await self.process_all_sms(sms_records)

        # Step 3: Write results to CSV
        self.write_results_to_csv(results)

        # Step 4: Generate and display summary
        summary = self.generate_summary_report(results)
        self.print_summary_report(summary)

        # Step 5: Save summary as JSON
        summary_file = self.output_csv_path.replace('.csv', '_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        print(f"\n⏱️ Total processing time: {processing_time:.2f} seconds")
        print(f"📄 Summary saved to: {summary_file}")
        print(f"✅ Processing completed successfully!")


# Convenience functions for easy usage
async def process_sms_backup_async(input_csv: str = 'sms_backup.csv', output_csv: str = 'sms_processed_results.csv'):
    """
    Async function to process SMS backup CSV file.
    """
    processor = SMSBackupProcessor(input_csv, output_csv)
    await processor.run_full_processing()


def process_sms_backup_sync(input_csv: str = 'sms_backup.csv', output_csv: str = 'sms_processed_results.csv'):
    """
    Synchronous wrapper for SMS backup processing.
    """
    asyncio.run(process_sms_backup_async(input_csv, output_csv))


# Main execution
if __name__ == "__main__":
    import sys

    # Allow command line arguments for input and output files
    input_file = sys.argv[1] if len(sys.argv) > 1 else 'sms_backup.csv'
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'sms_processed_results.csv'

    print(f"📥 Input file: {input_file}")
    print(f"📤 Output file: {output_file}")

    # Run the processing
    process_sms_backup_sync(input_file, output_file)
